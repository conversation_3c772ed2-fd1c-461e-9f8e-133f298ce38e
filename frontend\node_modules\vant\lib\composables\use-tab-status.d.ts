import { ComputedRef, InjectionKey } from 'vue';
export declare const TAB_STATUS_KEY: InjectionKey<ComputedRef<boolean>>;
export declare const ALL_TAB_STATUS_KEY: InjectionKey<ComputedRef<boolean>>;
export declare const useTabStatus: () => ComputedRef<boolean> | null;
export declare const useAllTabStatus: () => ComputedRef<boolean> | null;
export declare const useProvideTabStatus: (status: ComputedRef<boolean>) => void;

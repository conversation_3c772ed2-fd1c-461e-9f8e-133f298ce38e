export type ShareSheetThemeVars = {
    shareSheetHeaderPadding?: string;
    shareSheetTitleColor?: string;
    shareSheetTitleFontSize?: string;
    shareSheetTitleLineHeight?: number | string;
    shareSheetDescriptionColor?: string;
    shareSheetDescriptionFontSize?: string;
    shareSheetDescriptionLineHeight?: number | string;
    shareSheetIconSize?: string;
    shareSheetOptionNameColor?: string;
    shareSheetOptionNameFontSize?: string;
    shareSheetOptionDescriptionColor?: string;
    shareSheetOptionDescriptionFontSize?: string;
    shareSheetCancelButtonFontSize?: string;
    shareSheetCancelButtonHeight?: string;
    shareSheetCancelButtonBackground?: string;
};

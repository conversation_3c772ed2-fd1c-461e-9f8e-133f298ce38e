<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 护士心理健康管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            margin-right: 15px;
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
        }

        .profile-section {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            margin: 0 auto 15px;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .profile-name {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .profile-info {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .stats-container {
            display: flex;
            justify-content: space-around;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .menu-section {
            padding: 20px;
        }

        .menu-group {
            background: white;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            cursor: pointer;
            transition: background-color 0.2s;
            border-bottom: 1px solid #f0f0f0;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item:hover {
            background-color: #f8f9fa;
        }

        .menu-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin-right: 15px;
        }

        .icon-assessment {
            background: #e3f2fd;
            color: #1976d2;
        }

        .icon-history {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .icon-settings {
            background: #e8f5e8;
            color: #388e3c;
        }

        .icon-help {
            background: #fff3e0;
            color: #f57c00;
        }

        .icon-about {
            background: #fce4ec;
            color: #c2185b;
        }

        .menu-content {
            flex: 1;
        }

        .menu-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 3px;
        }

        .menu-desc {
            font-size: 14px;
            color: #7f8c8d;
        }

        .menu-arrow {
            color: #bdc3c7;
            font-size: 16px;
        }

        /* 评估历史页面 */
        .history-page {
            display: none;
        }

        .chart-container {
            background: white;
            margin: 20px;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }

        .chart-placeholder {
            height: 200px;
            background: #f8f9fa;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            margin-bottom: 20px;
        }

        .history-list {
            padding: 0 20px 20px;
        }

        .history-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .history-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .history-date {
            font-size: 12px;
            color: #95a5a6;
        }

        .history-score {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .score-value {
            font-size: 24px;
            font-weight: 700;
            color: #4facfe;
        }

        .score-level {
            font-size: 14px;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 个人中心主页 -->
        <div class="profile-main" id="profileMain">
            <div class="header">
                <button class="back-btn" onclick="history.back()">←</button>
                <div class="header-title">个人中心</div>
            </div>

            <div class="profile-section">
                <div class="profile-avatar">👩‍⚕️</div>
                <div class="profile-name">张护士</div>
                <div class="profile-info">内科护士 · 工号：N2024001</div>
                
                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-number">8</div>
                        <div class="stat-label">评估次数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">25</div>
                        <div class="stat-label">学习天数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">3</div>
                        <div class="stat-label">咨询次数</div>
                    </div>
                </div>
            </div>

            <div class="menu-section">
                <div class="menu-group">
                    <div class="menu-item" onclick="showHistory()">
                        <div class="menu-icon icon-assessment">📊</div>
                        <div class="menu-content">
                            <div class="menu-title">评估历史</div>
                            <div class="menu-desc">查看历次心理评估记录和趋势</div>
                        </div>
                        <div class="menu-arrow">›</div>
                    </div>
                    
                    <div class="menu-item" onclick="showLearningHistory()">
                        <div class="menu-icon icon-history">📚</div>
                        <div class="menu-content">
                            <div class="menu-title">学习记录</div>
                            <div class="menu-desc">查看知识学习历史和收藏</div>
                        </div>
                        <div class="menu-arrow">›</div>
                    </div>
                </div>

                <div class="menu-group">
                    <div class="menu-item" onclick="showSettings()">
                        <div class="menu-icon icon-settings">⚙️</div>
                        <div class="menu-content">
                            <div class="menu-title">设置</div>
                            <div class="menu-desc">个人信息、通知设置等</div>
                        </div>
                        <div class="menu-arrow">›</div>
                    </div>
                    
                    <div class="menu-item" onclick="showHelp()">
                        <div class="menu-icon icon-help">❓</div>
                        <div class="menu-content">
                            <div class="menu-title">帮助与反馈</div>
                            <div class="menu-desc">使用帮助和问题反馈</div>
                        </div>
                        <div class="menu-arrow">›</div>
                    </div>
                    
                    <div class="menu-item" onclick="showAbout()">
                        <div class="menu-icon icon-about">ℹ️</div>
                        <div class="menu-content">
                            <div class="menu-title">关于我们</div>
                            <div class="menu-desc">应用版本和团队信息</div>
                        </div>
                        <div class="menu-arrow">›</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 评估历史页面 -->
        <div class="history-page" id="historyPage">
            <div class="header">
                <button class="back-btn" onclick="backToProfile()">←</button>
                <div class="header-title">评估历史</div>
            </div>

            <div class="chart-container">
                <div class="chart-title">压力水平变化趋势</div>
                <div class="chart-placeholder">
                    📈 趋势图表（此处可集成图表库）
                </div>
            </div>

            <div class="history-list">
                <div class="history-item">
                    <div class="history-header">
                        <div class="history-title">压力知觉量表 (PSS)</div>
                        <div class="history-date">2024-08-05</div>
                    </div>
                    <div class="history-score">
                        <div class="score-value">26</div>
                        <div class="score-level">中等压力</div>
                    </div>
                </div>

                <div class="history-item">
                    <div class="history-header">
                        <div class="history-title">心理弹性量表 (CD-RISC)</div>
                        <div class="history-date">2024-08-03</div>
                    </div>
                    <div class="history-score">
                        <div class="score-value">68</div>
                        <div class="score-level">良好弹性</div>
                    </div>
                </div>

                <div class="history-item">
                    <div class="history-header">
                        <div class="history-title">压力知觉量表 (PSS)</div>
                        <div class="history-date">2024-07-05</div>
                    </div>
                    <div class="history-score">
                        <div class="score-value">32</div>
                        <div class="score-level">较高压力</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showHistory() {
            document.getElementById('profileMain').style.display = 'none';
            document.getElementById('historyPage').style.display = 'block';
        }

        function backToProfile() {
            document.getElementById('historyPage').style.display = 'none';
            document.getElementById('profileMain').style.display = 'block';
        }

        function showLearningHistory() {
            alert('学习记录功能开发中...');
        }

        function showSettings() {
            alert('设置功能开发中...');
        }

        function showHelp() {
            alert('帮助与反馈功能开发中...');
        }

        function showAbout() {
            alert('关于我们：护士心理健康管理平台 v1.0\n专为医护人员设计的心理健康支持应用');
        }
    </script>
</body>
</html>

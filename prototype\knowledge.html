<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识学习 - 护士心理健康管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            margin-right: 15px;
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
        }

        .search-container {
            padding: 15px 20px;
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .search-box {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e0e0e0;
            border-radius: 25px;
            font-size: 14px;
            background: #f8f9fa;
        }

        .categories {
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .category-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.2s;
            border-left: 4px solid;
        }

        .category-card:hover {
            transform: translateY(-3px);
        }

        .category-card.cognitive {
            border-left-color: #ff6b6b;
        }

        .category-card.emotion {
            border-left-color: #4ecdc4;
        }

        .category-card.social {
            border-left-color: #45b7d1;
        }

        .category-card.behavior {
            border-left-color: #96ceb4;
        }

        .category-icon {
            font-size: 30px;
            margin-bottom: 10px;
        }

        .category-title {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .category-count {
            font-size: 12px;
            color: #7f8c8d;
        }

        .recommended-section {
            padding: 0 20px 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .content-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .content-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .content-card:hover {
            transform: translateY(-2px);
        }

        .content-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .content-type {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-right: 10px;
        }

        .type-article {
            background: #e3f2fd;
            color: #1976d2;
        }

        .type-audio {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .type-video {
            background: #e8f5e8;
            color: #388e3c;
        }

        .content-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .content-desc {
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.4;
            margin-bottom: 10px;
        }

        .content-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #95a5a6;
        }

        .content-duration {
            display: flex;
            align-items: center;
        }

        .content-date {
            display: flex;
            align-items: center;
        }

        /* 内容详情页面 */
        .content-detail {
            display: none;
            padding: 20px;
        }

        .detail-header {
            margin-bottom: 20px;
        }

        .detail-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .detail-meta {
            display: flex;
            gap: 15px;
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 20px;
        }

        .audio-player {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .play-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            margin-bottom: 15px;
            transition: transform 0.2s;
        }

        .play-btn:hover {
            transform: scale(1.1);
        }

        .audio-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .audio-duration {
            font-size: 14px;
            color: #7f8c8d;
        }

        .content-body {
            font-size: 16px;
            line-height: 1.6;
            color: #2c3e50;
        }

        .content-body h3 {
            font-size: 18px;
            margin: 20px 0 10px;
            color: #2c3e50;
        }

        .content-body p {
            margin-bottom: 15px;
        }

        .content-body ul {
            margin: 15px 0;
            padding-left: 20px;
        }

        .content-body li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 知识库主页 -->
        <div class="knowledge-main" id="knowledgeMain">
            <div class="header">
                <button class="back-btn" onclick="history.back()">←</button>
                <div class="header-title">知识学习</div>
            </div>

            <div class="search-container">
                <input type="text" class="search-box" placeholder="搜索心理健康知识...">
            </div>

            <div class="categories">
                <div class="category-card cognitive" onclick="showCategory('cognitive')">
                    <div class="category-icon">🧠</div>
                    <div class="category-title">认知重构</div>
                    <div class="category-count">12篇内容</div>
                </div>
                <div class="category-card emotion" onclick="showCategory('emotion')">
                    <div class="category-icon">💆‍♀️</div>
                    <div class="category-title">情绪调节</div>
                    <div class="category-count">15篇内容</div>
                </div>
                <div class="category-card social" onclick="showCategory('social')">
                    <div class="category-icon">🤝</div>
                    <div class="category-title">社会支持</div>
                    <div class="category-count">8篇内容</div>
                </div>
                <div class="category-card behavior" onclick="showCategory('behavior')">
                    <div class="category-icon">🎯</div>
                    <div class="category-title">行为激活</div>
                    <div class="category-count">10篇内容</div>
                </div>
            </div>

            <div class="recommended-section">
                <h2 class="section-title">推荐内容</h2>
                <div class="content-list">
                    <div class="content-card" onclick="showContent('stress-management')">
                        <div class="content-header">
                            <span class="content-type type-article">文章</span>
                        </div>
                        <div class="content-title">工作压力管理的5个实用技巧</div>
                        <div class="content-desc">学习如何在繁忙的护理工作中有效管理压力，保持心理健康和工作效率。</div>
                        <div class="content-meta">
                            <div class="content-duration">📖 5分钟阅读</div>
                            <div class="content-date">📅 2024-08-01</div>
                        </div>
                    </div>

                    <div class="content-card" onclick="showContent('mindfulness-audio')">
                        <div class="content-header">
                            <span class="content-type type-audio">音频</span>
                        </div>
                        <div class="content-title">5分钟正念冥想练习</div>
                        <div class="content-desc">通过简单的正念练习，帮助您在工作间隙快速放松身心，缓解紧张情绪。</div>
                        <div class="content-meta">
                            <div class="content-duration">🎵 5分钟</div>
                            <div class="content-date">📅 2024-07-28</div>
                        </div>
                    </div>

                    <div class="content-card" onclick="showContent('communication-skills')">
                        <div class="content-header">
                            <span class="content-type type-video">视频</span>
                        </div>
                        <div class="content-title">与患者家属的有效沟通技巧</div>
                        <div class="content-desc">学习如何在困难情况下与患者家属进行有效沟通，减少冲突和误解。</div>
                        <div class="content-meta">
                            <div class="content-duration">📹 8分钟</div>
                            <div class="content-date">📅 2024-07-25</div>
                        </div>
                    </div>

                    <div class="content-card" onclick="showContent('self-care')">
                        <div class="content-header">
                            <span class="content-type type-article">文章</span>
                        </div>
                        <div class="content-title">护士自我关怀指南</div>
                        <div class="content-desc">了解自我关怀的重要性，学习在照顾他人的同时也要照顾好自己。</div>
                        <div class="content-meta">
                            <div class="content-duration">📖 7分钟阅读</div>
                            <div class="content-date">📅 2024-07-22</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内容详情页面 -->
        <div class="content-detail" id="contentDetail">
            <div class="header">
                <button class="back-btn" onclick="backToKnowledge()">←</button>
                <div class="header-title">内容详情</div>
            </div>

            <div class="detail-header">
                <div class="detail-title">5分钟正念冥想练习</div>
                <div class="detail-meta">
                    <span>🎵 音频</span>
                    <span>📅 2024-07-28</span>
                    <span>👁️ 1,234次播放</span>
                </div>
            </div>

            <div class="audio-player">
                <button class="play-btn" onclick="togglePlay()">▶️</button>
                <div class="audio-title">正念冥想引导音频</div>
                <div class="audio-duration">总时长：5分钟</div>
            </div>

            <div class="content-body">
                <h3>练习说明</h3>
                <p>这是一个专为医护人员设计的5分钟正念冥想练习。您可以在工作间隙、休息时间或下班后进行这个练习。</p>
                
                <h3>练习步骤</h3>
                <ul>
                    <li>找一个安静舒适的地方坐下</li>
                    <li>闭上眼睛，深呼吸三次</li>
                    <li>跟随音频指导，专注于呼吸</li>
                    <li>当思绪飘散时，温和地将注意力拉回呼吸</li>
                    <li>练习结束后，慢慢睁开眼睛</li>
                </ul>

                <h3>练习效果</h3>
                <p>定期进行正念冥想可以帮助您：</p>
                <ul>
                    <li>减少工作压力和焦虑</li>
                    <li>提高注意力和专注度</li>
                    <li>改善睡眠质量</li>
                    <li>增强情绪调节能力</li>
                    <li>提升整体幸福感</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function showCategory(category) {
            alert('显示' + category + '分类内容');
        }

        function showContent(contentId) {
            document.getElementById('knowledgeMain').style.display = 'none';
            document.getElementById('contentDetail').style.display = 'block';
        }

        function backToKnowledge() {
            document.getElementById('contentDetail').style.display = 'none';
            document.getElementById('knowledgeMain').style.display = 'block';
        }

        let isPlaying = false;
        function togglePlay() {
            const playBtn = document.querySelector('.play-btn');
            if (isPlaying) {
                playBtn.textContent = '▶️';
                isPlaying = false;
            } else {
                playBtn.textContent = '⏸️';
                isPlaying = true;
            }
        }
    </script>
</body>
</html>

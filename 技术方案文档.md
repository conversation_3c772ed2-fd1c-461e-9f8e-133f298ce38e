# 护士第二受害者心理健康管理H5应用 - 技术方案文档

## 项目概述
本项目是一个专为护士群体设计的心理健康管理H5应用，提供心理评估、知识学习、专业咨询等功能，帮助医护人员应对工作压力，维护心理健康。

## 技术架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (H5)     │    │   后端 API      │    │   数据存储      │
│                 │    │                 │    │                 │
│ Vue 3 + Vant UI │◄──►│ Node.js/Express │◄──►│    SQLite3      │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 前端技术栈

### 核心框架
- **Vue 3.3+** - 渐进式JavaScript框架，采用组合式API
- **Vant UI 4.6+** - 轻量、可靠的移动端组件库
- **Vue Router 4.2+** - 官方路由管理器
- **Pinia 2.1+** - 新一代状态管理库

### 开发工具
- **Vite 4.0+** - 现代化构建工具，提供快速的开发体验
- **TypeScript** - 可选，提供类型安全
- **ESLint + Prettier** - 代码规范和格式化
- **Sass/SCSS** - CSS预处理器

### 功能库
- **Axios 1.4+** - HTTP客户端，用于API请求
- **ECharts 5.4+** - 数据可视化图表库（评估趋势图）
- **Day.js 1.11+** - 轻量级日期处理库
- **VueUse** - Vue组合式工具集

### 前端项目结构
```
src/
├── assets/              # 静态资源
│   ├── images/         # 图片资源
│   ├── icons/          # 图标文件
│   └── styles/         # 全局样式
├── components/          # 公共组件
│   ├── common/         # 通用组件
│   │   ├── AppHeader.vue
│   │   ├── AppFooter.vue
│   │   └── LoadingSpinner.vue
│   ├── assessment/     # 评估相关组件
│   │   ├── AssessmentCard.vue
│   │   ├── QuestionItem.vue
│   │   └── ResultChart.vue
│   ├── knowledge/      # 知识库组件
│   │   ├── ContentCard.vue
│   │   ├── CategoryGrid.vue
│   │   └── MediaPlayer.vue
│   └── consultation/   # 咨询组件
│       ├── CounselorCard.vue
│       └── MessageBoard.vue
├── views/              # 页面组件
│   ├── Login/          # 登录模块
│   │   ├── LoginPage.vue
│   │   └── RegisterPage.vue
│   ├── Home/           # 首页模块
│   │   └── HomePage.vue
│   ├── Assessment/     # 评估模块
│   │   ├── AssessmentList.vue
│   │   ├── QuestionnaireView.vue
│   │   └── ResultView.vue
│   ├── Knowledge/      # 知识库模块
│   │   ├── KnowledgeHome.vue
│   │   ├── CategoryView.vue
│   │   └── ContentDetail.vue
│   ├── Consultation/   # 咨询模块
│   │   ├── ConsultationHome.vue
│   │   ├── CounselorList.vue
│   │   └── MessageBoard.vue
│   └── Profile/        # 个人中心
│       ├── ProfileHome.vue
│       ├── AssessmentHistory.vue
│       └── Settings.vue
├── store/              # 状态管理
│   ├── index.js        # Pinia配置
│   ├── user.js         # 用户状态
│   ├── assessment.js   # 评估状态
│   └── knowledge.js    # 知识库状态
├── api/                # API接口
│   ├── index.js        # Axios配置
│   ├── auth.js         # 认证接口
│   ├── assessment.js   # 评估接口
│   ├── knowledge.js    # 知识库接口
│   └── consultation.js # 咨询接口
├── utils/              # 工具函数
│   ├── request.js      # 请求封装
│   ├── storage.js      # 本地存储
│   ├── validation.js   # 表单验证
│   └── constants.js    # 常量定义
├── router/             # 路由配置
│   └── index.js
├── App.vue             # 根组件
└── main.js             # 入口文件
```

## 后端技术栈

### 核心框架
- **Node.js 18+** - JavaScript运行时环境
- **Express.js 4.18+** - Web应用框架
- **SQLite3** - 轻量级关系型数据库

### 开发工具和中间件
- **Nodemon** - 开发时自动重启
- **CORS** - 跨域资源共享
- **Morgan** - HTTP请求日志
- **Helmet** - 安全中间件
- **Express-rate-limit** - 请求频率限制

### 数据处理
- **sqlite3** - SQLite数据库驱动
- **bcryptjs** - 密码加密
- **jsonwebtoken** - JWT令牌生成和验证
- **multer** - 文件上传处理
- **joi** - 数据验证

### 后端项目结构
```
server/
├── config/             # 配置文件
│   ├── database.js     # 数据库配置
│   └── jwt.js          # JWT配置
├── controllers/        # 控制器
│   ├── authController.js
│   ├── assessmentController.js
│   ├── knowledgeController.js
│   └── consultationController.js
├── models/             # 数据模型
│   ├── User.js
│   ├── Assessment.js
│   ├── Knowledge.js
│   └── Consultation.js
├── routes/             # 路由定义
│   ├── auth.js
│   ├── assessment.js
│   ├── knowledge.js
│   └── consultation.js
├── middleware/         # 中间件
│   ├── auth.js         # 身份验证
│   ├── validation.js   # 数据验证
│   └── errorHandler.js # 错误处理
├── utils/              # 工具函数
│   ├── database.js     # 数据库工具
│   ├── logger.js       # 日志工具
│   └── helpers.js      # 辅助函数
├── uploads/            # 文件上传目录
├── database/           # 数据库文件
│   └── app.db          # SQLite数据库文件
├── app.js              # Express应用配置
└── server.js           # 服务器启动文件
```

## 数据库设计

### SQLite3 数据表结构

#### 用户表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    employee_id VARCHAR(50) UNIQUE,
    department VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 评估记录表 (assessments)
```sql
CREATE TABLE assessments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    scale_type VARCHAR(20) NOT NULL, -- 'PSS' or 'CD-RISC'
    time_point VARCHAR(10) NOT NULL, -- 'T1', 'T2', 'T3', 'T4'
    answers TEXT NOT NULL,           -- JSON格式存储答案
    total_score INTEGER NOT NULL,
    level VARCHAR(20),               -- 评估等级
    suggestions TEXT,                -- 建议内容
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 知识内容表 (knowledge_content)
```sql
CREATE TABLE knowledge_content (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(200) NOT NULL,
    category VARCHAR(50) NOT NULL,  -- 'cognitive', 'emotion', 'social', 'behavior'
    content_type VARCHAR(20) NOT NULL, -- 'article', 'audio', 'video'
    content TEXT NOT NULL,
    media_url VARCHAR(500),
    duration INTEGER,               -- 音频/视频时长(秒)
    view_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 咨询师表 (counselors)
```sql
CREATE TABLE counselors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    title VARCHAR(100),
    specialties TEXT,               -- JSON格式存储擅长领域
    description TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    avatar_url VARCHAR(500),
    rating DECIMAL(2,1) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'offline', -- 'online', 'busy', 'offline'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 匿名留言表 (anonymous_messages)
```sql
CREATE TABLE anonymous_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    content TEXT NOT NULL,
    reply TEXT,
    counselor_id INTEGER,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'replied'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    replied_at DATETIME,
    FOREIGN KEY (counselor_id) REFERENCES counselors(id)
);
```

## 核心功能模块

### 1. 用户认证模块
- JWT令牌认证
- 密码加密存储
- 登录状态管理
- 权限控制

### 2. 心理评估模块
- PSS压力知觉量表
- CD-RISC心理弹性量表
- T1-T4时间点数据追踪
- 评估结果分析和建议生成

### 3. 知识学习模块
- 分类内容管理
- 多媒体内容支持
- 搜索功能
- 学习记录统计

### 4. 心理咨询模块
- 咨询师信息展示
- 联系方式管理
- 匿名留言功能
- 24小时热线展示

### 5. 个人中心模块
- 用户信息管理
- 评估历史查看
- 数据统计展示
- 设置功能

## 开发环境配置

### 前端开发环境
```bash
# 安装依赖
npm install

# 开发服务器
npm run dev

# 构建生产版本
npm run build

# 代码检查
npm run lint
```

### 后端开发环境
```bash
# 安装依赖
npm install

# 开发模式启动
npm run dev

# 生产模式启动
npm start

# 数据库初始化
npm run db:init
```

## 部署方案

### 开发环境
- 前端：Vite开发服务器 (localhost:3000)
- 后端：Node.js服务器 (localhost:3001)
- 数据库：本地SQLite文件

### 生产环境
- 前端：Nginx静态文件服务
- 后端：PM2进程管理
- 数据库：SQLite文件存储
- HTTPS：SSL证书配置

## 安全考虑

### 数据安全
- 密码bcrypt加密
- JWT令牌认证
- 请求频率限制
- 输入数据验证

### 隐私保护
- 匿名评估选项
- 数据脱敏处理
- 访问日志记录
- 定期数据备份

## 性能优化

### 前端优化
- 组件懒加载
- 图片压缩和懒加载
- 代码分割
- 缓存策略

### 后端优化
- 数据库索引优化
- 接口响应缓存
- 文件压缩传输
- 错误监控

## 开发计划

### 第一阶段：基础功能开发 (2-3周)
- 用户认证系统
- 基础页面框架
- 数据库设计和初始化

### 第二阶段：核心功能开发 (3-4周)
- 心理评估模块
- 知识学习模块
- 个人中心基础功能

### 第三阶段：完善和优化 (2-3周)
- 心理咨询模块
- 数据统计和图表
- 性能优化和测试

### 第四阶段：测试和部署 (1-2周)
- 功能测试
- 用户体验优化
- 生产环境部署

## 技术风险评估

### 低风险
- Vue 3生态成熟稳定
- SQLite轻量级，适合中小型应用
- 开发团队技术栈匹配

### 中风险
- 移动端兼容性测试
- 数据备份和恢复策略
- 用户隐私合规要求

### 应对措施
- 充分的设备测试
- 定期数据备份机制
- 隐私政策和用户协议制定

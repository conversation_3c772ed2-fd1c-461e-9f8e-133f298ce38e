<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>心理评估 - 护士心理健康管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            margin-right: 15px;
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
        }

        .assessment-list {
            padding: 20px;
        }

        .assessment-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #4facfe;
        }

        .assessment-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .assessment-desc {
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .assessment-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .info-item {
            font-size: 12px;
            color: #95a5a6;
        }

        .time-points {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .time-point {
            padding: 5px 10px;
            background: #ecf0f1;
            border-radius: 15px;
            font-size: 12px;
            color: #7f8c8d;
        }

        .time-point.completed {
            background: #d5f4e6;
            color: #27ae60;
        }

        .time-point.current {
            background: #fff3cd;
            color: #856404;
        }

        .btn-start {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn-start:hover {
            transform: translateY(-2px);
        }

        .btn-view {
            width: 100%;
            padding: 12px;
            background: #ecf0f1;
            color: #7f8c8d;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
        }

        /* 量表填写页面 */
        .questionnaire-page {
            display: none;
        }

        .progress-bar {
            height: 4px;
            background: #ecf0f1;
            margin-bottom: 20px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            width: 20%;
            transition: width 0.3s;
        }

        .question-container {
            padding: 20px;
        }

        .question-number {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 10px;
        }

        .question-text {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 25px;
            line-height: 1.4;
        }

        .options {
            margin-bottom: 30px;
        }

        .option {
            background: white;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .option:hover {
            border-color: #4facfe;
        }

        .option.selected {
            border-color: #4facfe;
            background: #f0f8ff;
        }

        .option-text {
            font-size: 16px;
            color: #2c3e50;
        }

        .navigation {
            display: flex;
            gap: 15px;
            padding: 20px;
        }

        .btn-prev, .btn-next {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn-prev {
            background: #ecf0f1;
            color: #7f8c8d;
        }

        .btn-next {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
        }

        .btn-next:disabled {
            background: #ecf0f1;
            color: #bdc3c7;
            cursor: not-allowed;
        }

        /* 结果页面 */
        .result-page {
            display: none;
            padding: 20px;
            text-align: center;
        }

        .result-icon {
            font-size: 60px;
            margin-bottom: 20px;
        }

        .result-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .result-score {
            font-size: 36px;
            font-weight: 700;
            color: #4facfe;
            margin-bottom: 20px;
        }

        .result-desc {
            font-size: 16px;
            color: #7f8c8d;
            line-height: 1.5;
            margin-bottom: 30px;
        }

        .result-suggestions {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: left;
            margin-bottom: 30px;
        }

        .suggestions-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .suggestion-item {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }

        .suggestion-item::before {
            content: "•";
            color: #4facfe;
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 评估列表页面 -->
        <div class="assessment-list-page" id="assessmentList">
            <div class="header">
                <button class="back-btn" onclick="history.back()">←</button>
                <div class="header-title">心理评估</div>
            </div>

            <div class="assessment-list">
                <div class="assessment-card">
                    <div class="assessment-title">压力知觉量表 (PSS)</div>
                    <div class="assessment-desc">评估您在过去一个月中对生活事件的压力感知程度，帮助了解您的压力水平。</div>
                    
                    <div class="assessment-info">
                        <div class="info-item">题目数量：10题</div>
                        <div class="info-item">预计时间：3-5分钟</div>
                    </div>

                    <div class="time-points">
                        <div class="time-point completed">T1 已完成</div>
                        <div class="time-point current">T2 进行中</div>
                        <div class="time-point">T3 待完成</div>
                        <div class="time-point">T4 待完成</div>
                    </div>

                    <button class="btn-start" onclick="startAssessment('PSS')">开始评估</button>
                </div>

                <div class="assessment-card">
                    <div class="assessment-title">心理弹性量表 (CD-RISC)</div>
                    <div class="assessment-desc">评估您面对逆境、创伤或重大压力时的适应能力和恢复能力。</div>
                    
                    <div class="assessment-info">
                        <div class="info-item">题目数量：25题</div>
                        <div class="info-item">预计时间：8-10分钟</div>
                    </div>

                    <div class="time-points">
                        <div class="time-point completed">T1 已完成</div>
                        <div class="time-point">T2 待完成</div>
                        <div class="time-point">T3 待完成</div>
                        <div class="time-point">T4 待完成</div>
                    </div>

                    <button class="btn-view">查看历史记录</button>
                </div>
            </div>
        </div>

        <!-- 量表填写页面 -->
        <div class="questionnaire-page" id="questionnairePage">
            <div class="header">
                <button class="back-btn" onclick="backToList()">←</button>
                <div class="header-title">压力知觉量表</div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="question-container">
                <div class="question-number" id="questionNumber">第 1 题 / 共 10 题</div>
                <div class="question-text" id="questionText">
                    在过去的一个月中，您有多经常因为发生了意想不到的事情而感到心烦意乱？
                </div>

                <div class="options" id="optionsContainer">
                    <div class="option" onclick="selectOption(0)">
                        <div class="option-text">从不</div>
                    </div>
                    <div class="option" onclick="selectOption(1)">
                        <div class="option-text">几乎从不</div>
                    </div>
                    <div class="option" onclick="selectOption(2)">
                        <div class="option-text">有时</div>
                    </div>
                    <div class="option" onclick="selectOption(3)">
                        <div class="option-text">相当经常</div>
                    </div>
                    <div class="option" onclick="selectOption(4)">
                        <div class="option-text">非常经常</div>
                    </div>
                </div>
            </div>

            <div class="navigation">
                <button class="btn-prev" onclick="prevQuestion()">上一题</button>
                <button class="btn-next" id="nextBtn" onclick="nextQuestion()" disabled>下一题</button>
            </div>
        </div>

        <!-- 结果页面 -->
        <div class="result-page" id="resultPage">
            <div class="result-icon">📊</div>
            <div class="result-title">评估完成</div>
            <div class="result-score">26 分</div>
            <div class="result-desc">您的压力水平处于中等程度。这表明您在日常生活中会感受到一定的压力，但仍在可控范围内。</div>

            <div class="result-suggestions">
                <div class="suggestions-title">个性化建议</div>
                <div class="suggestion-item">尝试每天进行10-15分钟的深呼吸练习</div>
                <div class="suggestion-item">保持规律的作息时间，确保充足睡眠</div>
                <div class="suggestion-item">适当进行体育锻炼，如散步或瑜伽</div>
                <div class="suggestion-item">与同事或朋友分享您的感受</div>
            </div>

            <button class="btn-start" onclick="backToList()">返回评估列表</button>
        </div>
    </div>

    <script>
        let currentQuestion = 0;
        let answers = [];
        const totalQuestions = 10;

        function startAssessment(type) {
            document.getElementById('assessmentList').style.display = 'none';
            document.getElementById('questionnairePage').style.display = 'block';
            currentQuestion = 0;
            answers = [];
            updateQuestion();
        }

        function selectOption(value) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
            // 选中当前选项
            event.target.closest('.option').classList.add('selected');
            
            answers[currentQuestion] = value;
            document.getElementById('nextBtn').disabled = false;
        }

        function nextQuestion() {
            if (currentQuestion < totalQuestions - 1) {
                currentQuestion++;
                updateQuestion();
            } else {
                showResult();
            }
        }

        function prevQuestion() {
            if (currentQuestion > 0) {
                currentQuestion--;
                updateQuestion();
            }
        }

        function updateQuestion() {
            document.getElementById('questionNumber').textContent = `第 ${currentQuestion + 1} 题 / 共 ${totalQuestions} 题`;
            
            const progress = ((currentQuestion + 1) / totalQuestions) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            
            // 清除选择状态
            document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
            
            // 如果有之前的答案，恢复选择状态
            if (answers[currentQuestion] !== undefined) {
                document.querySelectorAll('.option')[answers[currentQuestion]].classList.add('selected');
                document.getElementById('nextBtn').disabled = false;
            } else {
                document.getElementById('nextBtn').disabled = true;
            }

            // 更新按钮文本
            if (currentQuestion === totalQuestions - 1) {
                document.getElementById('nextBtn').textContent = '完成评估';
            } else {
                document.getElementById('nextBtn').textContent = '下一题';
            }
        }

        function showResult() {
            document.getElementById('questionnairePage').style.display = 'none';
            document.getElementById('resultPage').style.display = 'block';
        }

        function backToList() {
            document.getElementById('questionnairePage').style.display = 'none';
            document.getElementById('resultPage').style.display = 'none';
            document.getElementById('assessmentList').style.display = 'block';
        }
    </script>
</body>
</html>

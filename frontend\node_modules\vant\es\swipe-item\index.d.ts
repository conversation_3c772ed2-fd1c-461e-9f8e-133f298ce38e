export declare const SwipeItem: import("../utils").WithInstall<import("vue").DefineComponent<{}, (() => import("vue/jsx-runtime").JSX.Element) | undefined, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>>;
export default SwipeItem;
declare module 'vue' {
    interface GlobalComponents {
        VanSwipeItem: typeof SwipeItem;
    }
}

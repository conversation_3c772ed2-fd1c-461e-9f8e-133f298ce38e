<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>心理咨询 - 护士心理健康管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            margin-right: 15px;
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
        }

        .emergency-section {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 20px;
            margin: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
        }

        .emergency-icon {
            font-size: 40px;
            margin-bottom: 10px;
        }

        .emergency-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .emergency-desc {
            font-size: 14px;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .emergency-btn {
            background: white;
            color: #ff6b6b;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .emergency-btn:hover {
            transform: translateY(-2px);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin: 20px 20px 15px;
            color: #2c3e50;
        }

        .counselor-list {
            padding: 0 20px 20px;
        }

        .counselor-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }

        .counselor-card:hover {
            transform: translateY(-3px);
        }

        .counselor-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .counselor-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-right: 15px;
        }

        .counselor-info {
            flex: 1;
        }

        .counselor-name {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .counselor-title {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 5px;
        }

        .counselor-rating {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #f39c12;
        }

        .specialties {
            margin-bottom: 15px;
        }

        .specialty-tag {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 8px;
            margin-bottom: 5px;
        }

        .counselor-desc {
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.4;
            margin-bottom: 15px;
        }

        .contact-info {
            display: flex;
            gap: 10px;
        }

        .contact-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn-call {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
        }

        .btn-message {
            background: #ecf0f1;
            color: #7f8c8d;
        }

        .contact-btn:hover {
            transform: translateY(-2px);
        }

        .online-status {
            display: flex;
            align-items: center;
            font-size: 12px;
            margin-top: 10px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-online {
            background: #27ae60;
        }

        .status-busy {
            background: #f39c12;
        }

        .status-offline {
            background: #95a5a6;
        }

        /* 匿名留言板 */
        .message-board {
            padding: 20px;
            background: #f8f9fa;
        }

        .message-form {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .form-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .message-textarea {
            width: 100%;
            min-height: 100px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            font-size: 14px;
            resize: vertical;
            margin-bottom: 15px;
        }

        .submit-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
        }

        .message-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .message-author {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
        }

        .message-date {
            font-size: 12px;
            color: #95a5a6;
        }

        .message-content {
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.4;
            margin-bottom: 10px;
        }

        .message-reply {
            background: #f8f9fa;
            border-left: 3px solid #4facfe;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
            color: #2c3e50;
        }

        .reply-author {
            font-size: 12px;
            color: #4facfe;
            font-weight: 600;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="history.back()">←</button>
            <div class="header-title">心理咨询</div>
        </div>

        <!-- 紧急求助 -->
        <div class="emergency-section">
            <div class="emergency-icon">🆘</div>
            <div class="emergency-title">24小时心理援助热线</div>
            <div class="emergency-desc">如果您正在经历严重的心理困扰，请立即拨打我们的24小时热线</div>
            <button class="emergency-btn" onclick="callEmergency()">************</button>
        </div>

        <!-- 咨询师列表 -->
        <h2 class="section-title">专业咨询师</h2>
        <div class="counselor-list">
            <div class="counselor-card">
                <div class="counselor-header">
                    <div class="counselor-avatar">👩‍⚕️</div>
                    <div class="counselor-info">
                        <div class="counselor-name">李心怡</div>
                        <div class="counselor-title">主任心理咨询师</div>
                        <div class="counselor-rating">
                            ⭐⭐⭐⭐⭐ 4.9分 (128评价)
                        </div>
                    </div>
                </div>
                
                <div class="specialties">
                    <span class="specialty-tag">工作压力</span>
                    <span class="specialty-tag">创伤后应激</span>
                    <span class="specialty-tag">职业倦怠</span>
                </div>
                
                <div class="counselor-desc">
                    从事心理咨询工作15年，专注于医护人员心理健康，擅长认知行为疗法和正念疗法。
                </div>
                
                <div class="contact-info">
                    <button class="contact-btn btn-call" onclick="callCounselor('13800138001')">📞 电话咨询</button>
                    <button class="contact-btn btn-message" onclick="messageCounselor()">💬 在线咨询</button>
                </div>
                
                <div class="online-status">
                    <div class="status-dot status-online"></div>
                    <span>在线 - 可立即咨询</span>
                </div>
            </div>

            <div class="counselor-card">
                <div class="counselor-header">
                    <div class="counselor-avatar">👨‍⚕️</div>
                    <div class="counselor-info">
                        <div class="counselor-name">王建国</div>
                        <div class="counselor-title">副主任心理咨询师</div>
                        <div class="counselor-rating">
                            ⭐⭐⭐⭐⭐ 4.8分 (95评价)
                        </div>
                    </div>
                </div>
                
                <div class="specialties">
                    <span class="specialty-tag">情绪调节</span>
                    <span class="specialty-tag">人际关系</span>
                    <span class="specialty-tag">睡眠障碍</span>
                </div>
                
                <div class="counselor-desc">
                    心理学博士，具有丰富的临床经验，特别擅长帮助医护人员处理工作中的情绪问题。
                </div>
                
                <div class="contact-info">
                    <button class="contact-btn btn-call" onclick="callCounselor('13800138002')">📞 电话咨询</button>
                    <button class="contact-btn btn-message" onclick="messageCounselor()">💬 在线咨询</button>
                </div>
                
                <div class="online-status">
                    <div class="status-dot status-busy"></div>
                    <span>忙碌中 - 预计30分钟后可咨询</span>
                </div>
            </div>

            <div class="counselor-card">
                <div class="counselor-header">
                    <div class="counselor-avatar">👩‍💼</div>
                    <div class="counselor-info">
                        <div class="counselor-name">张美丽</div>
                        <div class="counselor-title">心理咨询师</div>
                        <div class="counselor-rating">
                            ⭐⭐⭐⭐⭐ 4.7分 (76评价)
                        </div>
                    </div>
                </div>
                
                <div class="specialties">
                    <span class="specialty-tag">焦虑抑郁</span>
                    <span class="specialty-tag">自我关怀</span>
                    <span class="specialty-tag">心理弹性</span>
                </div>
                
                <div class="counselor-desc">
                    国家二级心理咨询师，专注于医护人员心理健康支持，温和耐心的咨询风格深受好评。
                </div>
                
                <div class="contact-info">
                    <button class="contact-btn btn-call" onclick="callCounselor('13800138003')">📞 电话咨询</button>
                    <button class="contact-btn btn-message" onclick="messageCounselor()">💬 在线咨询</button>
                </div>
                
                <div class="online-status">
                    <div class="status-dot status-offline"></div>
                    <span>离线 - 明天9:00上线</span>
                </div>
            </div>
        </div>

        <!-- 匿名留言板 -->
        <h2 class="section-title">匿名留言板</h2>
        <div class="message-board">
            <div class="message-form">
                <div class="form-title">匿名提问</div>
                <textarea class="message-textarea" placeholder="请描述您的困扰或问题，我们会尽快回复您..."></textarea>
                <button class="submit-btn" onclick="submitMessage()">提交留言</button>
            </div>

            <div class="message-item">
                <div class="message-header">
                    <div class="message-author">匿名用户</div>
                    <div class="message-date">2024-08-05</div>
                </div>
                <div class="message-content">
                    最近工作压力很大，经常失眠，感觉很焦虑，不知道该怎么办？
                </div>
                <div class="message-reply">
                    <div class="reply-author">李心怡咨询师 回复：</div>
                    工作压力导致的失眠和焦虑是很常见的问题。建议您：1）建立规律的作息时间；2）睡前进行放松练习；3）适当运动释放压力。如果症状持续，建议寻求专业帮助。
                </div>
            </div>

            <div class="message-item">
                <div class="message-header">
                    <div class="message-author">匿名用户</div>
                    <div class="message-date">2024-08-04</div>
                </div>
                <div class="message-content">
                    在工作中遇到医疗事故，感觉很自责，怎么调节这种情绪？
                </div>
                <div class="message-reply">
                    <div class="reply-author">王建国咨询师 回复：</div>
                    医疗事故后的自责情绪是正常的反应。重要的是要理解这不完全是个人责任，医疗工作本身就存在风险。建议您与同事或专业人士交流，寻求情感支持，必要时可以进行专业心理咨询。
                </div>
            </div>
        </div>
    </div>

    <script>
        function callEmergency() {
            if (confirm('确定要拨打24小时心理援助热线 ************ 吗？')) {
                window.location.href = 'tel:************';
            }
        }

        function callCounselor(phone) {
            if (confirm('确定要拨打咨询师电话 ' + phone + ' 吗？')) {
                window.location.href = 'tel:' + phone;
            }
        }

        function messageCounselor() {
            alert('在线咨询功能正在开发中，请稍后再试或直接拨打电话咨询。');
        }

        function submitMessage() {
            const textarea = document.querySelector('.message-textarea');
            if (textarea.value.trim()) {
                alert('留言提交成功！我们会在24小时内回复您。');
                textarea.value = '';
            } else {
                alert('请输入您的问题或困扰。');
            }
        }
    </script>
</body>
</html>

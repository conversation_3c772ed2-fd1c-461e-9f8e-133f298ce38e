<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>护士心理健康管理H5应用 - 原型演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .demo-header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .demo-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .demo-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .demo-description {
            font-size: 16px;
            opacity: 0.8;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .prototype-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s, box-shadow 0.3s;
            text-align: center;
        }

        .prototype-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .card-icon {
            font-size: 60px;
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .card-description {
            font-size: 16px;
            color: #7f8c8d;
            line-height: 1.5;
            margin-bottom: 25px;
        }

        .card-features {
            text-align: left;
            margin-bottom: 25px;
        }

        .feature-item {
            font-size: 14px;
            color: #34495e;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .feature-item::before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .demo-btn {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: transform 0.2s;
        }

        .demo-btn:hover {
            transform: translateY(-2px);
        }

        .mobile-frame {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 375px;
            height: 667px;
            background: #000;
            border-radius: 30px;
            padding: 10px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
        }

        .mobile-screen {
            width: 100%;
            height: 100%;
            border-radius: 20px;
            overflow: hidden;
            background: white;
        }

        .mobile-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        .close-btn {
            position: absolute;
            top: -40px;
            right: 0;
            background: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            color: #333;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 999;
            display: none;
        }

        .tech-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            color: white;
            text-align: center;
        }

        .tech-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .tech-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
        }

        .tech-item h4 {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .tech-item p {
            font-size: 14px;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .demo-container {
                padding: 10px;
            }

            .demo-title {
                font-size: 24px;
            }

            .demo-subtitle {
                font-size: 16px;
            }

            .prototype-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .mobile-frame {
                width: 90vw;
                height: 80vh;
                max-width: 375px;
                max-height: 667px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">护士心理健康管理H5应用</h1>
            <p class="demo-subtitle">原型设计演示</p>
            <p class="demo-description">
                专为护士群体设计的心理健康支持平台，提供心理评估、知识学习、专业咨询等功能，
                帮助医护人员应对工作压力，维护心理健康。以下是各个功能模块的原型演示。
            </p>
        </div>

        <div class="prototype-grid">
            <div class="prototype-card">
                <div class="card-icon">🏠</div>
                <h3 class="card-title">登录 & 首页</h3>
                <p class="card-description">用户登录界面和应用首页，提供快速功能入口和个性化推荐内容。</p>
                <div class="card-features">
                    <div class="feature-item">简洁的登录界面设计</div>
                    <div class="feature-item">个性化欢迎信息</div>
                    <div class="feature-item">快速功能入口</div>
                    <div class="feature-item">智能内容推荐</div>
                    <div class="feature-item">底部导航栏</div>
                </div>
                <a href="#" class="demo-btn" onclick="showDemo('index.html')">查看演示</a>
            </div>

            <div class="prototype-card">
                <div class="card-icon">📋</div>
                <h3 class="card-title">心理评估</h3>
                <p class="card-description">专业的心理量表评估系统，支持PSS和CD-RISC量表，提供详细的评估报告。</p>
                <div class="card-features">
                    <div class="feature-item">PSS压力知觉量表</div>
                    <div class="feature-item">CD-RISC心理弹性量表</div>
                    <div class="feature-item">T1-T4时间点追踪</div>
                    <div class="feature-item">即时评估报告</div>
                    <div class="feature-item">个性化建议</div>
                </div>
                <a href="#" class="demo-btn" onclick="showDemo('assessment.html')">查看演示</a>
            </div>

            <div class="prototype-card">
                <div class="card-icon">📚</div>
                <h3 class="card-title">知识学习</h3>
                <p class="card-description">丰富的心理健康知识库，包含文章、音频、视频等多媒体内容，支持分类浏览。</p>
                <div class="card-features">
                    <div class="feature-item">四大分类内容库</div>
                    <div class="feature-item">多媒体学习资源</div>
                    <div class="feature-item">智能内容推荐</div>
                    <div class="feature-item">搜索功能</div>
                    <div class="feature-item">学习进度追踪</div>
                </div>
                <a href="#" class="demo-btn" onclick="showDemo('knowledge.html')">查看演示</a>
            </div>

            <div class="prototype-card">
                <div class="card-icon">💬</div>
                <h3 class="card-title">心理咨询</h3>
                <p class="card-description">专业心理咨询师信息展示，提供多种联系方式和24小时心理援助热线。</p>
                <div class="card-features">
                    <div class="feature-item">专业咨询师列表</div>
                    <div class="feature-item">咨询师详细信息</div>
                    <div class="feature-item">多种联系方式</div>
                    <div class="feature-item">24小时援助热线</div>
                    <div class="feature-item">匿名留言板</div>
                </div>
                <a href="#" class="demo-btn" onclick="showDemo('consultation.html')">查看演示</a>
            </div>

            <div class="prototype-card">
                <div class="card-icon">👤</div>
                <h3 class="card-title">个人中心</h3>
                <p class="card-description">用户个人信息管理，评估历史查看，学习记录统计等个性化功能。</p>
                <div class="card-features">
                    <div class="feature-item">个人信息展示</div>
                    <div class="feature-item">评估历史记录</div>
                    <div class="feature-item">趋势图表分析</div>
                    <div class="feature-item">学习统计数据</div>
                    <div class="feature-item">设置和帮助</div>
                </div>
                <a href="#" class="demo-btn" onclick="showDemo('profile.html')">查看演示</a>
            </div>
        </div>

        <div class="tech-info">
            <h2 class="tech-title">技术特性</h2>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>📱 响应式设计</h4>
                    <p>H5技术实现，完美适配各种移动设备</p>
                </div>
                <div class="tech-item">
                    <h4>🎨 用户体验</h4>
                    <p>简洁友好的界面，温和的色彩搭配</p>
                </div>
                <div class="tech-item">
                    <h4>🔒 数据安全</h4>
                    <p>用户隐私保护，数据加密存储</p>
                </div>
                <div class="tech-item">
                    <h4>⚡ 性能优化</h4>
                    <p>快速加载，流畅交互体验</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 移动端演示框架 -->
    <div class="overlay" id="overlay" onclick="closeDemo()"></div>
    <div class="mobile-frame" id="mobileFrame">
        <button class="close-btn" onclick="closeDemo()">×</button>
        <div class="mobile-screen">
            <iframe class="mobile-iframe" id="demoIframe" src=""></iframe>
        </div>
    </div>

    <script>
        function showDemo(page) {
            document.getElementById('overlay').style.display = 'block';
            document.getElementById('mobileFrame').style.display = 'block';
            document.getElementById('demoIframe').src = page;
            document.body.style.overflow = 'hidden';
        }

        function closeDemo() {
            document.getElementById('overlay').style.display = 'none';
            document.getElementById('mobileFrame').style.display = 'none';
            document.getElementById('demoIframe').src = '';
            document.body.style.overflow = 'auto';
        }

        // ESC键关闭演示
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeDemo();
            }
        });
    </script>
</body>
</html>

import { type ExtractPropTypes } from 'vue';
export declare const indexAnchorProps: {
    index: (NumberConstructor | StringConstructor)[];
};
export type IndexAnchorProps = ExtractPropTypes<typeof indexAnchorProps>;
declare const _default: import("vue").DefineComponent<ExtractPropTypes<{
    index: (NumberConstructor | StringConstructor)[];
}>, (() => import("vue/jsx-runtime").JSX.Element) | undefined, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<ExtractPropTypes<{
    index: (NumberConstructor | StringConstructor)[];
}>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>护士心理健康管理平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
        }

        /* 登录页面 */
        .login-page {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 40px 30px;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .logo {
            width: 80px;
            height: 80px;
            background: #4facfe;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);
        }

        .logo::before {
            content: "💚";
            font-size: 40px;
        }

        .app-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            text-align: center;
        }

        .app-subtitle {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 40px;
            text-align: center;
        }

        .form-group {
            width: 100%;
            margin-bottom: 20px;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
            transition: transform 0.2s;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
        }

        .link-text {
            color: #4facfe;
            text-decoration: none;
            font-size: 14px;
            margin-top: 20px;
        }

        /* 首页样式 */
        .home-page {
            display: none;
        }

        .header {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .welcome-text {
            font-size: 18px;
            margin-bottom: 5px;
        }

        .date-text {
            font-size: 14px;
            opacity: 0.9;
        }

        .quick-actions {
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .action-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .action-card:hover {
            transform: translateY(-3px);
        }

        .action-icon {
            font-size: 30px;
            margin-bottom: 10px;
        }

        .action-title {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
        }

        .recommended-section {
            padding: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .content-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .content-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .content-desc {
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.4;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 375px;
            background: white;
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
            box-shadow: 0 -3px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            padding: 5px;
            color: #7f8c8d;
            transition: color 0.2s;
        }

        .nav-item.active {
            color: #4facfe;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 3px;
        }

        .nav-text {
            font-size: 12px;
        }

        /* 响应式设计 */
        @media (max-width: 320px) {
            .container {
                max-width: 100%;
            }
            
            .login-page {
                padding: 30px 20px;
            }
            
            .app-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 登录页面 -->
        <div class="login-page" id="loginPage">
            <div class="logo"></div>
            <h1 class="app-title">护士心理健康管理</h1>
            <p class="app-subtitle">关爱每一位医护工作者的心理健康</p>
            
            <div class="form-group">
                <input type="text" class="form-input" placeholder="请输入手机号或工号">
            </div>
            
            <div class="form-group">
                <input type="password" class="form-input" placeholder="请输入密码">
            </div>
            
            <button class="btn-primary" onclick="showHomePage()">登录</button>
            
            <a href="#" class="link-text">还没有账号？立即注册</a>
        </div>

        <!-- 首页 -->
        <div class="home-page" id="homePage">
            <div class="header">
                <div class="welcome-text">你好，张护士</div>
                <div class="date-text">今天是2024年8月6日</div>
            </div>

            <div class="quick-actions">
                <div class="action-card" onclick="showPage('assessment')">
                    <div class="action-icon">📋</div>
                    <div class="action-title">心理评估</div>
                </div>
                <div class="action-card" onclick="showPage('knowledge')">
                    <div class="action-icon">📚</div>
                    <div class="action-title">知识学习</div>
                </div>
                <div class="action-card" onclick="showPage('consultation')">
                    <div class="action-icon">👩‍⚕️</div>
                    <div class="action-title">心理咨询</div>
                </div>
                <div class="action-card" onclick="showPage('profile')">
                    <div class="action-icon">👤</div>
                    <div class="action-title">个人中心</div>
                </div>
            </div>

            <div class="recommended-section">
                <h2 class="section-title">为您推荐</h2>
                <div class="content-card">
                    <div class="content-title">压力管理小技巧</div>
                    <div class="content-desc">学习简单有效的压力缓解方法，帮助您在工作中保持心理平衡</div>
                </div>
                <div class="content-card">
                    <div class="content-title">正念冥想音频</div>
                    <div class="content-desc">5分钟正念练习，随时随地放松身心，缓解工作压力</div>
                </div>
            </div>

            <div style="height: 80px;"></div> <!-- 底部导航占位 -->
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active">
                <div class="nav-icon">🏠</div>
                <div class="nav-text">首页</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon">📋</div>
                <div class="nav-text">评估</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon">📚</div>
                <div class="nav-text">知识</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon">💬</div>
                <div class="nav-text">咨询</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon">👤</div>
                <div class="nav-text">我的</div>
            </div>
        </div>
    </div>

    <script>
        function showHomePage() {
            document.getElementById('loginPage').style.display = 'none';
            document.getElementById('homePage').style.display = 'block';
        }

        function showPage(page) {
            alert('跳转到' + page + '页面');
        }

        // 底部导航切换
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>

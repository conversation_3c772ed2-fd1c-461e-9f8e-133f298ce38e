export type ActionSheetThemeVars = {
    actionSheetMaxHeight?: string;
    actionSheetHeaderHeight?: string;
    actionSheetHeaderFontSize?: string;
    actionSheetDescriptionColor?: string;
    actionSheetDescriptionFontSize?: string;
    actionSheetDescriptionLineHeight?: number | string;
    actionSheetItemBackground?: string;
    actionSheetItemFontSize?: string;
    actionSheetItemLineHeight?: number | string;
    actionSheetItemTextColor?: string;
    actionSheetItemDisabledTextColor?: string;
    actionSheetSubnameColor?: string;
    actionSheetSubnameFontSize?: string;
    actionSheetSubnameLineHeight?: number | string;
    actionSheetCloseIconSize?: string;
    actionSheetCloseIconColor?: string;
    actionSheetCloseIconPadding?: string;
    actionSheetCancelTextColor?: string;
    actionSheetCancelPaddingTop?: string;
    actionSheetCancelPaddingColor?: string;
    actionSheetLoadingIconSize?: string;
};
